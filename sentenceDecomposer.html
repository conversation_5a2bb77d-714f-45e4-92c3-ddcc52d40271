<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chinese Learning Companion</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .character-card {
            transition: all 0.3s ease;
        }

        .character-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        .tone-1 {
            color: #f44336;
        }

        /* Red for first tone */
        .tone-2 {
            color: #ff9800;
        }

        /* Orange for second tone */
        .tone-3 {
            color: #4caf50;
        }

        /* Green for third tone */
        .tone-4 {
            color: #2196f3;
        }

        /* Blue for fourth tone */
        .tone-5 {
            color: #9c27b0;
        }

        /* Purple for neutral tone */
        .stroke-order {
            background-color: #f3f4f6;
            border-radius: 0.5rem;
        }

        .stroke-order svg {
            width: 100%;
            height: auto;
        }

        #saved-sentences-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .saved-sentence {
            border-left: 3px solid #3b82f6;
            transition: all 0.2s;
        }

        .saved-sentence:hover {
            background-color: #f0f5ff;
            border-left-width: 5px;
        }
    </style>
</head>

<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <header class="mb-8 text-center">
            <h1 class="text-3xl md:text-4xl font-bold text-gray-800 mb-2">中文学习助手</h1>
            <p class="text-lg text-gray-600">Chinese Learning Companion</p>
            <div class="w-24 h-1 bg-blue-500 mx-auto mt-4"></div>
        </header>

        <!-- Main Content -->
        <div class="flex flex-col lg:flex-row gap-8">
            <!-- Input Section -->
            <div class="lg:w-1/2 bg-white rounded-xl shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">输入中文句子 (Enter Chinese Sentence)</h2>

                <form id="sentence-form" class="mb-6">
                    <div class="relative">
                        <input type="text" id="chinese-input" placeholder="例如: 你好，我学习中文"
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-lg">
                        <button type="submit"
                            class="absolute right-2 top-1/2 transform -translate-y-1/2 bg-blue-500 text-white p-2 rounded-lg hover:bg-blue-600 transition">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>

                <!-- Features -->
                <div class="space-y-4">
                    <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                        <div class="bg-blue-100 p-2 rounded-full mr-3">
                            <i class="fas fa-volume-up text-blue-500"></i>
                        </div>
                        <div>
                            <h3 class="font-medium">发音指南 (Pronunciation Guide)</h3>
                            <p class="text-sm text-gray-500">获取带声调标记的拼音</p>
                        </div>
                    </div>

                    <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                        <div class="bg-green-100 p-2 rounded-full mr-3">
                            <i class="fas fa-font text-green-500"></i>
                        </div>
                        <div>
                            <h3 class="font-medium">汉字分解 (Character Breakdown)</h3>
                            <p class="text-sm text-gray-500">每个汉字的结构、部首和笔画</p>
                        </div>
                    </div>

                    <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                        <div class="bg-purple-100 p-2 rounded-full mr-3">
                            <i class="fas fa-book text-purple-500"></i>
                        </div>
                        <div>
                            <h3 class="font-medium">词汇解释 (Word Explanation)</h3>
                            <p class="text-sm text-gray-500">词语的定义和使用示例</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Results Section -->
            <div class="lg:w-1/2">
                <div class="bg-white rounded-xl shadow-md p-6 mb-6">
                    <h2 class="text-xl font-semibold text-gray-800 mb-4">分析结果 (Analysis Results)</h2>

                    <div id="loading" class="hidden text-center py-8">
                        <i class="fas fa-spinner fa-spin text-3xl text-blue-500"></i>
                        <p class="mt-2 text-gray-600">分析中... (Analyzing...)</p>
                    </div>

                    <div id="results" class="hidden space-y-6">
                        <!-- Sentence Overview -->
                        <div class="p-4 bg-blue-50 rounded-lg">
                            <h3 class="font-medium text-blue-800 mb-2">句子 (Sentence)</h3>
                            <p id="sentence-chinese" class="text-2xl mb-2"></p>
                            <p id="sentence-pinyin" class="text-lg"></p>

                            <div class="flex justify-between items-center mt-4">
                                <button id="play-sentence"
                                    class="flex items-center bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600">
                                    <i class="fas fa-volume-up mr-2"></i> 播放
                                </button>
                                <button id="save-sentence"
                                    class="flex items-center bg-gray-200 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-300">
                                    <i class="fas fa-bookmark mr-2"></i> 保存
                                </button>
                            </div>
                        </div>

                        <!-- Translation -->
                        <div class="p-4 bg-green-50 rounded-lg">
                            <h3 class="font-medium text-green-800 mb-2">翻译 (Translation)</h3>
                            <p id="sentence-translation" class="text-lg"></p>
                        </div>

                        <!-- Character Breakdown -->
                        <div>
                            <h3 class="font-medium text-gray-800 mb-3">汉字解析 (Character Breakdown)</h3>
                            <div id="character-cards" class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3">
                            </div>
                        </div>
                    </div>

                    <!-- Empty State -->
                    <div id="empty-state" class="text-center py-8">
                        <i class="fas fa-comment-slash text-4xl text-gray-300 mb-3"></i>
                        <h3 class="text-lg text-gray-500">输入一个中文句子开始分析</h3>
                        <p class="text-gray-400">Enter a Chinese sentence to begin analysis</p>
                    </div>
                </div>

                <!-- Saved Sentences -->
                <div class="bg-white rounded-xl shadow-md p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-xl font-semibold text-gray-800">保存的句子 (Saved Sentences)</h2>
                        <button id="clear-saved" class="text-sm text-red-500 hover:text-red-700">
                            <i class="fas fa-trash mr-1"></i> Clear All
                        </button>
                    </div>

                    <div id="saved-sentences" class="hidden">
                        <ul id="saved-sentences-list" class="space-y-2"></ul>
                    </div>

                    <div id="no-saved" class="text-center py-4">
                        <p class="text-gray-500">没有保存的句子</p>
                        <p class="text-sm text-gray-400">分析句子后可以点击保存按钮</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Character Detail Modal -->
    <div id="character-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-xl max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div class="p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-bold" id="modal-character"></h3>
                    <button id="close-modal" class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <div class="grid grid-cols-2 gap-4">
                    <div class="bg-gray-50 p-3 rounded-lg">
                        <h4 class="font-medium text-gray-700 mb-2">拼音 (Pinyin)</h4>
                        <p id="modal-pinyin" class="text-lg"></p>
                    </div>

                    <div class="bg-gray-50 p-3 rounded-lg">
                        <h4 class="font-medium text-gray-700 mb-2">意思 (Meaning)</h4>
                        <p id="modal-meaning"></p>
                    </div>

                    <div class="bg-gray-50 p-3 rounded-lg">
                        <h4 class="font-medium text-gray-700 mb-2">部首 (Radical)</h4>
                        <p id="modal-radical"></p>
                    </div>

                    <div class="bg-gray-50 p-3 rounded-lg">
                        <h4 class="font-medium text-gray-700 mb-2">笔画数 (Strokes)</h4>
                        <p id="modal-strokes"></p>
                    </div>

                    <div class="col-span-2 bg-gray-50 p-3 rounded-lg">
                        <h4 class="font-medium text-gray-700 mb-2">笔顺 (Stroke Order)</h4>
                        <div class="stroke-order p-4 flex justify-center">
                            <svg id="stroke-order-svg" width="120" height="120" viewBox="0 0 120 120" fill="none"
                                xmlns="http://www.w3.org/2000/svg"></svg>
                        </div>
                    </div>

                    <div class="col-span-2">
                        <button id="play-character"
                            class="w-full bg-blue-500 text-white py-2 rounded-lg hover:bg-blue-600">
                            <i class="fas fa-volume-up mr-2"></i> 播放发音
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const form = document.getElementById('sentence-form');
            const input = document.getElementById('chinese-input');
            const results = document.getElementById('results');
            const loading = document.getElementById('loading');
            const emptyState = document.getElementById('empty-state');
            const savedSentences = document.getElementById('saved-sentences');
            const noSaved = document.getElementById('no-saved');
            const savedSentencesList = document.getElementById('saved-sentences-list');
            const clearSavedBtn = document.getElementById('clear-saved');
            const saveSentenceBtn = document.getElementById('save-sentence');
            const playSentenceBtn = document.getElementById('play-sentence');
            const characterModal = document.getElementById('character-modal');
            const closeModalBtn = document.getElementById('close-modal');
            const playCharacterBtn = document.getElementById('play-character');

            // Modal elements
            const modalCharacter = document.getElementById('modal-character');
            const modalPinyin = document.getElementById('modal-pinyin');
            const modalMeaning = document.getElementById('modal-meaning');
            const modalRadical = document.getElementById('modal-radical');
            const modalStrokes = document.getElementById('modal-strokes');
            const strokeOrderSvg = document.getElementById('stroke-order-svg');

            // Sample data - in a real app, this would come from an API
            const dictionary = {
                "你": {
                    pinyin: "nǐ",
                    tone: 3,
                    meaning: "you",
                    radical: "亻",
                    strokes: 7,
                    strokeOrder: [
                        "M20,20 L20,100",
                        "M20,20 L100,20",
                        "M20,50 L80,50",
                        "M50,20 L50,100",
                        "M80,20 L80,100",
                        "M20,100 L80,100",
                        "M50,70 L80,70"
                    ]
                },
                "好": {
                    pinyin: "hǎo",
                    tone: 3,
                    meaning: "good, well",
                    radical: "女",
                    strokes: 6,
                    strokeOrder: [
                        "M30,20 L30,60",
                        "M20,70 L40,70",
                        "M20,40 L40,40",
                        "M70,20 L90,50 L70,80",
                        "M60,50 L80,20 L100,50"
                    ]
                },
                "我": {
                    pinyin: "wǒ",
                    tone: 3,
                    meaning: "I, me",
                    radical: "戈",
                    strokes: 7,
                    strokeOrder: [
                        "M30,20 L30,100",
                        "M20,30 L40,30",
                        "M20,20 L40,20",
                        "M60,20 L80,20",
                        "M70,20 L70,100",
                        "M60,100 L80,100",
                        "M40,60 L85,60"
                    ]
                },
                "学习": {
                    pinyin: "xué xí",
                    tone: [2, 2],
                    meaning: "to study, to learn",
                    radical: ["子", "⺮"],
                    strokes: [8, 11],
                    isWord: true
                },
                "中文": {
                    pinyin: "zhōng wén",
                    tone: [1, 2],
                    meaning: "Chinese language",
                    radical: ["丨", "文"],
                    strokes: [4, 4],
                    isWord: true
                },
                "你好": {
                    pinyin: "nǐ hǎo",
                    tone: [3, 3],
                    meaning: "hello",
                    radical: ["亻", "女"],
                    strokes: [7, 6],
                    isWord: true
                }
            };

            // Saved sentences in localStorage
            let savedSentencesData = JSON.parse(localStorage.getItem('chineseSavedSentences')) || [];

            // Check if we have saved sentences
            updateSavedSentencesUI();

            // Form submission
            form.addEventListener('submit', function (e) {
                e.preventDefault();
                const sentence = input.value.trim();

                if (!sentence) {
                    alert('Please enter a Chinese sentence');
                    return;
                }

                analyzeSentence(sentence);
            });

            function analyzeSentence(sentence) {
                // Show loading, hide empty state
                loading.classList.remove('hidden');
                emptyState.classList.add('hidden');
                results.classList.add('hidden');

                // Simulate API call delay
                setTimeout(() => {
                    loading.classList.add('hidden');
                    displayResults(sentence);
                }, 800);
            }

            function displayResults(sentence) {
                // Display the Chinese sentence
                document.getElementById('sentence-chinese').textContent = sentence;

                // Generate pinyin - in a real app, this would use an API
                const words = splitSentence(sentence);
                let pinyinParts = [];
                let charCount = 0;

                words.forEach(word => {
                    if (dictionary[word]) {
                        const wordData = dictionary[word];
                        if (wordData.isWord) {
                            // It's a multi-character word
                            pinyinParts.push(addToneColors(wordData.pinyin, wordData.tone));
                            charCount += word.length;
                        } else {
                            // Single character
                            pinyinParts.push(addToneColors(wordData.pinyin, wordData.tone));
                            charCount += 1;
                        }
                    } else {
                        // Unknown word/character - just display as-is
                        pinyinParts.push(word);
                        charCount += word.length;
                    }
                });

                document.getElementById('sentence-pinyin').innerHTML = pinyinParts.join(' ');

                // Generate translation - in a real app, this would use a translation API
                let translationParts = [];
                let englishParts = [];

                words.forEach(word => {
                    if (dictionary[word]) {
                        translationParts.push(`${word} (${dictionary[word].pinyin}): ${dictionary[word].meaning}`);
                        englishParts.push(dictionary[word].meaning);
                    } else {
                        translationParts.push(`${word}: [unknown]`);
                        englishParts.push('[unknown]');
                    }
                });

                document.getElementById('sentence-translation').textContent = englishParts.join(' ');

                // Create character/word cards
                const characterCards = document.getElementById('character-cards');
                characterCards.innerHTML = '';

                words.forEach(word => {
                    const card = document.createElement('div');
                    card.className = 'character-card bg-white border border-gray-200 rounded-lg p-3 cursor-pointer hover:shadow-md';

                    if (dictionary[word]) {
                        const wordData = dictionary[word];

                        if (wordData.isWord) {
                            // It's a multi-character word
                            card.innerHTML = `
                                <div class="flex justify-between items-start">
                                    <div>
                                        <div class="text-2xl font-bold mb-1">${word}</div>
                                        <div class="text-sm text-gray-600 mb-1">${wordData.radical.join(' + ')}</div>
                                        <div class="text-sm">${addToneColors(wordData.pinyin, wordData.tone)}</div>
                                    </div>
                                    <div class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
                                        Word
                                    </div>
                                </div>
                                <div class="mt-2 text-sm text-gray-700">${wordData.meaning}</div>
                            `;
                        } else {
                            // Single character
                            card.innerHTML = `
                                <div class="text-2xl font-bold mb-1">${word}</div>
                                <div class="text-sm text-gray-600 mb-1">Radical: ${wordData.radical}</div>
                                <div class="text-sm mb-2">${addToneColors(wordData.pinyin, wordData.tone)}</div>
                                <div class="text-sm text-gray-700">${wordData.meaning}</div>
                            `;
                        }

                        // Add click handler for character details
                        card.addEventListener('click', () => showCharacterModal(word));

                    } else {
                        // Unknown character/word
                        card.innerHTML = `
                            <div class="text-2xl font-bold mb-1">${word}</div>
                            <div class="text-sm text-gray-500">Unknown character/word</div>
                        `;
                    }

                    characterCards.appendChild(card);
                });

                // Show results
                results.classList.remove('hidden');
            }

            function splitSentence(sentence) {
                // This is a simplistic approach - in reality Chinese word segmentation is complex
                const words = [];
                let i = 0;

                while (i < sentence.length) {
                    // Check for multi-character words first
                    if (i + 1 < sentence.length) {
                        const twoChar = sentence.substring(i, i + 2);
                        if (dictionary[twoChar] && dictionary[twoChar].isWord) {
                            words.push(twoChar);
                            i += 2;
                            continue;
                        }
                    }

                    // Single character
                    words.push(sentence[i]);
                    i += 1;
                }

                return words;
            }

            function addToneColors(pinyin, tones) {
                if (!Array.isArray(tones)) {
                    tones = [tones];
                }

                const syllables = pinyin.split(' ');
                let result = [];

                for (let i = 0; i < syllables.length && i < tones.length; i++) {
                    const tone = tones[i];
                    const toneClass = `tone-${tone}`;
                    result.push(`<span class="${toneClass} font-medium">${syllables[i]}</span>`);
                }

                return result.join(' ');
            }

            function showCharacterModal(character) {
                if (!dictionary[character]) return;

                const data = dictionary[character];

                modalCharacter.textContent = character;

                if (Array.isArray(data.pinyin)) {
                    modalPinyin.innerHTML = addToneColors(data.pinyin.join(' '), data.tone);
                } else {
                    modalPinyin.innerHTML = addToneColors(data.pinyin, data.tone);
                }

                if (Array.isArray(data.meaning)) {
                    modalMeaning.textContent = data.meaning.join(', ');
                } else {
                    modalMeaning.textContent = data.meaning;
                }

                if (Array.isArray(data.radical)) {
                    modalRadical.textContent = data.radical.join(' + ');
                } else {
                    modalRadical.textContent = data.radical;
                }

                if (Array.isArray(data.strokes)) {
                    modalStrokes.textContent = data.strokes.join(' + ');
                } else {
                    modalStrokes.textContent = data.strokes;
                }

                // Draw stroke order
                if (data.strokeOrder) {
                    strokeOrderSvg.innerHTML = '';
                    data.strokeOrder.forEach((stroke, index) => {
                        // Create path for each stroke
                        const path = document.createElementNS("http://www.w3.org/2000/svg", "path");
                        path.setAttribute("d", stroke);
                        path.setAttribute("stroke", "#3b82f6");
                        path.setAttribute("stroke-width", "3");
                        path.setAttribute("stroke-linecap", "round");
                        path.setAttribute("fill", "none");

                        // Create circle with stroke number
                        const circle = document.createElementNS("http://www.w3.org/2000/svg", "circle");
                        circle.setAttribute("cx", stroke.split(' ')[1].replace('L', ''));
                        circle.setAttribute("cy", stroke.split(' ')[2]);
                        circle.setAttribute("r", "10");
                        circle.setAttribute("fill", "#3b82f6");

                        const text = document.createElementNS("http://www.w3.org/2000/svg", "text");
                        text.setAttribute("x", stroke.split(' ')[1].replace('L', ''));
                        text.setAttribute("y", stroke.split(' ')[2]);
                        text.setAttribute("text-anchor", "middle");
                        text.setAttribute("dominant-baseline", "middle");
                        text.setAttribute("fill", "white");
                        text.setAttribute("font-size", "10");
                        text.textContent = (index + 1).toString();

                        strokeOrderSvg.appendChild(path);
                        strokeOrderSvg.appendChild(circle);
                        strokeOrderSvg.appendChild(text);
                    });
                }

                characterModal.classList.remove('hidden');
            }

            closeModalBtn.addEventListener('click', () => {
                characterModal.classList.add('hidden');
            });

            // Save sentence functionality
            saveSentenceBtn.addEventListener('click', function () {
                const sentence = document.getElementById('sentence-chinese').textContent;
                const pinyin = document.getElementById('sentence-pinyin').textContent;
                const translation = document.getElementById('sentence-translation').textContent;

                if (!sentence) return;

                // Check if already saved
                if (savedSentencesData.some(item => item.sentence === sentence)) {
                    alert('This sentence is already saved!');
                    return;
                }

                savedSentencesData.push({
                    sentence,
                    pinyin,
                    translation,
                    date: new Date().toISOString()
                });

                localStorage.setItem('chineseSavedSentences', JSON.stringify(savedSentencesData));
                updateSavedSentencesUI();

                // Change button style temporarily
                this.innerHTML = '<i class="fas fa-check mr-2"></i> 已保存';
                this.classList.remove('bg-gray-200', 'text-gray-700');
                this.classList.add('bg-green-500', 'text-white');

                setTimeout(() => {
                    this.innerHTML = '<i class="fas fa-bookmark mr-2"></i> 保存';
                    this.classList.remove('bg-green-500', 'text-white');
                    this.classList.add('bg-gray-200', 'text-gray-700');
                }, 1500);
            });

            // Update saved sentences UI
            function updateSavedSentencesUI() {
                if (savedSentencesData.length > 0) {
                    savedSentences.classList.remove('hidden');
                    noSaved.classList.add('hidden');

                    savedSentencesList.innerHTML = '';

                    savedSentencesData.forEach((item, index) => {
                        const li = document.createElement('li');
                        li.className = 'saved-sentence p-3 bg-gray-50 rounded cursor-pointer';
                        li.innerHTML = `
                            <div class="flex justify-between">
                                <div class="font-medium">${item.sentence}</div>
                                <button class="text-gray-400 hover:text-red-500" data-index="${index}">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                            <div class="text-sm text-gray-600">${item.pinyin}</div>
                            <div class="text-sm text-gray-500 mt-1">${item.translation}</div>
                        `;

                        // Add click handler to load saved sentence
                        li.addEventListener('click', function (e) {
                            if (e.target.tagName === 'BUTTON' || e.target.tagName === 'I') return;
                            input.value = item.sentence;
                            analyzeSentence(item.sentence);

                            // Scroll to top
                            window.scrollTo({
                                top: 0,
                                behavior: 'smooth'
                            });
                        });

                        // Add delete handler for the X button
                        const deleteBtn = li.querySelector('button');
                        deleteBtn.addEventListener('click', function (e) {
                            e.stopPropagation();
                            const index = parseInt(this.getAttribute('data-index'));
                            savedSentencesData.splice(index, 1);
                            localStorage.setItem('chineseSavedSentences', JSON.stringify(savedSentencesData));
                            updateSavedSentencesUI();
                        });

                        savedSentencesList.appendChild(li);
                    });
                } else {
                    savedSentences.classList.add('hidden');
                    noSaved.classList.remove('hidden');
                }
            }

            // Clear all saved sentences
            clearSavedBtn.addEventListener('click', function () {
                if (confirm('Are you sure you want to delete all saved sentences?')) {
                    savedSentencesData = [];
                    localStorage.setItem('chineseSavedSentences', JSON.stringify(savedSentencesData));
                    updateSavedSentencesUI();
                }
            });

            // Play audio functionality (simulated)
            playSentenceBtn.addEventListener('click', function () {
                const sentence = document.getElementById('sentence-chinese').textContent;

                // In a real app, this would use TTS or pre-recorded audio
                alert(`Playing pronunciation for: ${sentence}`);
            });

            playCharacterBtn.addEventListener('click', function () {
                const character = modalCharacter.textContent;

                // In a real app, this would use TTS or pre-recorded audio
                alert(`Playing pronunciation for: ${character}`);
            });
        });
    </script>
</body>

</html>