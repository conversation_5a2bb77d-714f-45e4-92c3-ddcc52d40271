<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chinese Learning Companion</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .character-card {
            transition: all 0.3s ease;
        }

        .character-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        .tone-1 {
            color: #f44336;
        }

        /* Red for first tone */
        .tone-2 {
            color: #ff9800;
        }

        /* Orange for second tone */
        .tone-3 {
            color: #4caf50;
        }

        /* Green for third tone */
        .tone-4 {
            color: #2196f3;
        }

        /* Blue for fourth tone */
        .tone-5 {
            color: #9c27b0;
        }

        /* Purple for neutral tone */
        .stroke-order {
            background-color: #f3f4f6;
            border-radius: 0.5rem;
        }

        .stroke-order svg {
            width: 100%;
            height: auto;
        }

        #saved-sentences-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .saved-sentence {
            border-left: 3px solid #3b82f6;
            transition: all 0.2s;
        }

        .saved-sentence:hover {
            background-color: #f0f5ff;
            border-left-width: 5px;
        }
    </style>
</head>

<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <header class="mb-8 text-center">
            <h1 class="text-3xl md:text-4xl font-bold text-gray-800 mb-2">中文学习助手</h1>
            <p class="text-lg text-gray-600">Chinese Learning Companion</p>
            <div class="w-24 h-1 bg-blue-500 mx-auto mt-4"></div>
        </header>

        <!-- Main Content -->
        <div class="flex flex-col lg:flex-row gap-8">
            <!-- Input Section -->
            <div class="lg:w-1/2 bg-white rounded-xl shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">输入中文句子 (Enter Chinese Sentence)</h2>

                <form id="sentence-form" class="mb-6">
                    <div class="relative">
                        <input type="text" id="chinese-input" placeholder="例如: 你好，我学习中文"
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-lg">
                        <button type="submit"
                            class="absolute right-2 top-1/2 transform -translate-y-1/2 bg-blue-500 text-white p-2 rounded-lg hover:bg-blue-600 transition">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>

                <!-- Features -->
                <div class="space-y-4">
                    <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                        <div class="bg-blue-100 p-2 rounded-full mr-3">
                            <i class="fas fa-volume-up text-blue-500"></i>
                        </div>
                        <div>
                            <h3 class="font-medium">发音指南 (Pronunciation Guide)</h3>
                            <p class="text-sm text-gray-500">获取带声调标记的拼音</p>
                        </div>
                    </div>

                    <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                        <div class="bg-green-100 p-2 rounded-full mr-3">
                            <i class="fas fa-font text-green-500"></i>
                        </div>
                        <div>
                            <h3 class="font-medium">汉字分解 (Character Breakdown)</h3>
                            <p class="text-sm text-gray-500">每个汉字的结构、部首和笔画</p>
                        </div>
                    </div>

                    <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                        <div class="bg-purple-100 p-2 rounded-full mr-3">
                            <i class="fas fa-book text-purple-500"></i>
                        </div>
                        <div>
                            <h3 class="font-medium">词汇解释 (Word Explanation)</h3>
                            <p class="text-sm text-gray-500">词语的定义和使用示例</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Results Section -->
            <div class="lg:w-1/2">
                <div class="bg-white rounded-xl shadow-md p-6 mb-6">
                    <h2 class="text-xl font-semibold text-gray-800 mb-4">分析结果 (Analysis Results)</h2>

                    <div id="loading" class="hidden text-center py-8">
                        <i class="fas fa-spinner fa-spin text-3xl text-blue-500"></i>
                        <p class="mt-2 text-gray-600">分析中... (Analyzing...)</p>
                    </div>

                    <div id="results" class="hidden space-y-6">
                        <!-- Sentence Overview -->
                        <div class="p-4 bg-blue-50 rounded-lg">
                            <h3 class="font-medium text-blue-800 mb-2">句子 (Sentence)</h3>
                            <p id="sentence-chinese" class="text-2xl mb-2"></p>
                            <p id="sentence-pinyin" class="text-lg"></p>

                            <div class="flex justify-between items-center mt-4">
                                <button id="play-sentence"
                                    class="flex items-center bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600">
                                    <i class="fas fa-volume-up mr-2"></i> 播放
                                </button>
                                <button id="save-sentence"
                                    class="flex items-center bg-gray-200 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-300">
                                    <i class="fas fa-bookmark mr-2"></i> 保存
                                </button>
                            </div>
                        </div>

                        <!-- Translation -->
                        <div class="p-4 bg-green-50 rounded-lg">
                            <h3 class="font-medium text-green-800 mb-2">翻译 (Translation)</h3>
                            <p id="sentence-translation" class="text-lg"></p>
                        </div>

                        <!-- Character Breakdown -->
                        <div>
                            <h3 class="font-medium text-gray-800 mb-3">汉字解析 (Character Breakdown)</h3>
                            <div id="character-cards" class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3">
                            </div>
                        </div>
                    </div>

                    <!-- Empty State -->
                    <div id="empty-state" class="text-center py-8">
                        <i class="fas fa-comment-slash text-4xl text-gray-300 mb-3"></i>
                        <h3 class="text-lg text-gray-500">输入一个中文句子开始分析</h3>
                        <p class="text-gray-400">Enter a Chinese sentence to begin analysis</p>
                    </div>
                </div>

                <!-- Saved Sentences -->
                <div class="bg-white rounded-xl shadow-md p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-xl font-semibold text-gray-800">保存的句子 (Saved Sentences)</h2>
                        <button id="clear-saved" class="text-sm text-red-500 hover:text-red-700">
                            <i class="fas fa-trash mr-1"></i> Clear All
                        </button>
                    </div>

                    <div id="saved-sentences" class="hidden">
                        <ul id="saved-sentences-list" class="space-y-2"></ul>
                    </div>

                    <div id="no-saved" class="text-center py-4">
                        <p class="text-gray-500">没有保存的句子</p>
                        <p class="text-sm text-gray-400">分析句子后可以点击保存按钮</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Character Detail Modal -->
    <div id="character-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-xl max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div class="p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-bold" id="modal-character"></h3>
                    <button id="close-modal" class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <div class="grid grid-cols-2 gap-4">
                    <div class="bg-gray-50 p-3 rounded-lg">
                        <h4 class="font-medium text-gray-700 mb-2">拼音 (Pinyin)</h4>
                        <p id="modal-pinyin" class="text-lg"></p>
                    </div>

                    <div class="bg-gray-50 p-3 rounded-lg">
                        <h4 class="font-medium text-gray-700 mb-2">意思 (Meaning)</h4>
                        <p id="modal-meaning"></p>
                    </div>

                    <div class="bg-gray-50 p-3 rounded-lg">
                        <h4 class="font-medium text-gray-700 mb-2">部首 (Radical)</h4>
                        <p id="modal-radical"></p>
                    </div>

                    <div class="bg-gray-50 p-3 rounded-lg">
                        <h4 class="font-medium text-gray-700 mb-2">笔画数 (Strokes)</h4>
                        <p id="modal-strokes"></p>
                    </div>

                    <div class="col-span-2 bg-gray-50 p-3 rounded-lg">
                        <h4 class="font-medium text-gray-700 mb-2">笔顺 (Stroke Order)</h4>
                        <div class="stroke-order p-4 flex justify-center">
                            <svg id="stroke-order-svg" width="120" height="120" viewBox="0 0 120 120" fill="none"
                                xmlns="http://www.w3.org/2000/svg"></svg>
                        </div>
                    </div>

                    <div class="col-span-2">
                        <button id="play-character"
                            class="w-full bg-blue-500 text-white py-2 rounded-lg hover:bg-blue-600">
                            <i class="fas fa-volume-up mr-2"></i> 播放发音
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Test Suite -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mocha/10.2.0/mocha.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/chai/4.3.7/chai.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/mocha/10.2.0/mocha.min.css">

    <!-- Test Results Container -->
    <div id="test-container" style="display: none;">
        <div id="mocha"></div>
    </div>

    <script>
        // Global variables for testing
        let app = {};

        document.addEventListener('DOMContentLoaded', function () {
            const form = document.getElementById('sentence-form');
            const input = document.getElementById('chinese-input');
            const results = document.getElementById('results');
            const loading = document.getElementById('loading');
            const emptyState = document.getElementById('empty-state');
            const savedSentences = document.getElementById('saved-sentences');
            const noSaved = document.getElementById('no-saved');
            const savedSentencesList = document.getElementById('saved-sentences-list');
            const clearSavedBtn = document.getElementById('clear-saved');
            const saveSentenceBtn = document.getElementById('save-sentence');
            const playSentenceBtn = document.getElementById('play-sentence');
            const characterModal = document.getElementById('character-modal');
            const closeModalBtn = document.getElementById('close-modal');
            const playCharacterBtn = document.getElementById('play-character');

            // Modal elements
            const modalCharacter = document.getElementById('modal-character');
            const modalPinyin = document.getElementById('modal-pinyin');
            const modalMeaning = document.getElementById('modal-meaning');
            const modalRadical = document.getElementById('modal-radical');
            const modalStrokes = document.getElementById('modal-strokes');
            const strokeOrderSvg = document.getElementById('stroke-order-svg');

            // Sample data - in a real app, this would come from an API
            const dictionary = {
                "你": {
                    pinyin: "nǐ",
                    tone: 3,
                    meaning: "you",
                    radical: "亻",
                    strokes: 7,
                    strokeOrder: [
                        "M20,20 L20,100",
                        "M20,20 L100,20",
                        "M20,50 L80,50",
                        "M50,20 L50,100",
                        "M80,20 L80,100",
                        "M20,100 L80,100",
                        "M50,70 L80,70"
                    ]
                },
                "好": {
                    pinyin: "hǎo",
                    tone: 3,
                    meaning: "good, well",
                    radical: "女",
                    strokes: 6,
                    strokeOrder: [
                        "M30,20 L30,60",
                        "M20,70 L40,70",
                        "M20,40 L40,40",
                        "M70,20 L90,50 L70,80",
                        "M60,50 L80,20 L100,50"
                    ]
                },
                "我": {
                    pinyin: "wǒ",
                    tone: 3,
                    meaning: "I, me",
                    radical: "戈",
                    strokes: 7,
                    strokeOrder: [
                        "M30,20 L30,100",
                        "M20,30 L40,30",
                        "M20,20 L40,20",
                        "M60,20 L80,20",
                        "M70,20 L70,100",
                        "M60,100 L80,100",
                        "M40,60 L85,60"
                    ]
                },
                "学习": {
                    pinyin: "xué xí",
                    tone: [2, 2],
                    meaning: "to study, to learn",
                    radical: ["子", "⺮"],
                    strokes: [8, 11],
                    isWord: true
                },
                "中文": {
                    pinyin: "zhōng wén",
                    tone: [1, 2],
                    meaning: "Chinese language",
                    radical: ["丨", "文"],
                    strokes: [4, 4],
                    isWord: true
                },
                "你好": {
                    pinyin: "nǐ hǎo",
                    tone: [3, 3],
                    meaning: "hello",
                    radical: ["亻", "女"],
                    strokes: [7, 6],
                    isWord: true
                }
            };

            // Saved sentences in localStorage
            let savedSentencesData = JSON.parse(localStorage.getItem('chineseSavedSentences')) || [];

            // Check if we have saved sentences
            updateSavedSentencesUI();

            // Form submission
            form.addEventListener('submit', function (e) {
                e.preventDefault();
                const sentence = input.value.trim();

                if (!sentence) {
                    alert('Please enter a Chinese sentence');
                    return;
                }

                analyzeSentence(sentence);
            });

            function analyzeSentence(sentence) {
                // Show loading, hide empty state
                loading.classList.remove('hidden');
                emptyState.classList.add('hidden');
                results.classList.add('hidden');

                // Simulate API call delay
                setTimeout(() => {
                    loading.classList.add('hidden');
                    displayResults(sentence);
                }, 800);
            }

            function displayResults(sentence) {
                // Display the Chinese sentence
                document.getElementById('sentence-chinese').textContent = sentence;

                // Generate pinyin - in a real app, this would use an API
                const words = splitSentence(sentence);
                let pinyinParts = [];
                let charCount = 0;

                words.forEach(word => {
                    if (dictionary[word]) {
                        const wordData = dictionary[word];
                        if (wordData.isWord) {
                            // It's a multi-character word
                            pinyinParts.push(addToneColors(wordData.pinyin, wordData.tone));
                            charCount += word.length;
                        } else {
                            // Single character
                            pinyinParts.push(addToneColors(wordData.pinyin, wordData.tone));
                            charCount += 1;
                        }
                    } else {
                        // Unknown word/character - just display as-is
                        pinyinParts.push(word);
                        charCount += word.length;
                    }
                });

                document.getElementById('sentence-pinyin').innerHTML = pinyinParts.join(' ');

                // Generate translation - in a real app, this would use a translation API
                let translationParts = [];
                let englishParts = [];

                words.forEach(word => {
                    if (dictionary[word]) {
                        translationParts.push(`${word} (${dictionary[word].pinyin}): ${dictionary[word].meaning}`);
                        englishParts.push(dictionary[word].meaning);
                    } else {
                        translationParts.push(`${word}: [unknown]`);
                        englishParts.push('[unknown]');
                    }
                });

                document.getElementById('sentence-translation').textContent = englishParts.join(' ');

                // Create character/word cards
                const characterCards = document.getElementById('character-cards');
                characterCards.innerHTML = '';

                words.forEach(word => {
                    const card = document.createElement('div');
                    card.className = 'character-card bg-white border border-gray-200 rounded-lg p-3 cursor-pointer hover:shadow-md';

                    if (dictionary[word]) {
                        const wordData = dictionary[word];

                        if (wordData.isWord) {
                            // It's a multi-character word
                            card.innerHTML = `
                                <div class="flex justify-between items-start">
                                    <div>
                                        <div class="text-2xl font-bold mb-1">${word}</div>
                                        <div class="text-sm text-gray-600 mb-1">${wordData.radical.join(' + ')}</div>
                                        <div class="text-sm">${addToneColors(wordData.pinyin, wordData.tone)}</div>
                                    </div>
                                    <div class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
                                        Word
                                    </div>
                                </div>
                                <div class="mt-2 text-sm text-gray-700">${wordData.meaning}</div>
                            `;
                        } else {
                            // Single character
                            card.innerHTML = `
                                <div class="text-2xl font-bold mb-1">${word}</div>
                                <div class="text-sm text-gray-600 mb-1">Radical: ${wordData.radical}</div>
                                <div class="text-sm mb-2">${addToneColors(wordData.pinyin, wordData.tone)}</div>
                                <div class="text-sm text-gray-700">${wordData.meaning}</div>
                            `;
                        }

                        // Add click handler for character details
                        card.addEventListener('click', () => showCharacterModal(word));

                    } else {
                        // Unknown character/word
                        card.innerHTML = `
                            <div class="text-2xl font-bold mb-1">${word}</div>
                            <div class="text-sm text-gray-500">Unknown character/word</div>
                        `;
                    }

                    characterCards.appendChild(card);
                });

                // Show results
                results.classList.remove('hidden');
            }

            function splitSentence(sentence) {
                // This is a simplistic approach - in reality Chinese word segmentation is complex
                const words = [];
                let i = 0;

                while (i < sentence.length) {
                    // Check for multi-character words first
                    if (i + 1 < sentence.length) {
                        const twoChar = sentence.substring(i, i + 2);
                        if (dictionary[twoChar] && dictionary[twoChar].isWord) {
                            words.push(twoChar);
                            i += 2;
                            continue;
                        }
                    }

                    // Single character
                    words.push(sentence[i]);
                    i += 1;
                }

                return words;
            }

            function addToneColors(pinyin, tones) {
                if (!Array.isArray(tones)) {
                    tones = [tones];
                }

                const syllables = pinyin.split(' ');
                let result = [];

                for (let i = 0; i < syllables.length && i < tones.length; i++) {
                    const tone = tones[i];
                    const toneClass = `tone-${tone}`;
                    result.push(`<span class="${toneClass} font-medium">${syllables[i]}</span>`);
                }

                return result.join(' ');
            }

            function showCharacterModal(character) {
                if (!dictionary[character]) return;

                const data = dictionary[character];

                modalCharacter.textContent = character;

                if (Array.isArray(data.pinyin)) {
                    modalPinyin.innerHTML = addToneColors(data.pinyin.join(' '), data.tone);
                } else {
                    modalPinyin.innerHTML = addToneColors(data.pinyin, data.tone);
                }

                if (Array.isArray(data.meaning)) {
                    modalMeaning.textContent = data.meaning.join(', ');
                } else {
                    modalMeaning.textContent = data.meaning;
                }

                if (Array.isArray(data.radical)) {
                    modalRadical.textContent = data.radical.join(' + ');
                } else {
                    modalRadical.textContent = data.radical;
                }

                if (Array.isArray(data.strokes)) {
                    modalStrokes.textContent = data.strokes.join(' + ');
                } else {
                    modalStrokes.textContent = data.strokes;
                }

                // Draw stroke order
                if (data.strokeOrder) {
                    strokeOrderSvg.innerHTML = '';
                    data.strokeOrder.forEach((stroke, index) => {
                        // Create path for each stroke
                        const path = document.createElementNS("http://www.w3.org/2000/svg", "path");
                        path.setAttribute("d", stroke);
                        path.setAttribute("stroke", "#3b82f6");
                        path.setAttribute("stroke-width", "3");
                        path.setAttribute("stroke-linecap", "round");
                        path.setAttribute("fill", "none");

                        // Create circle with stroke number
                        const circle = document.createElementNS("http://www.w3.org/2000/svg", "circle");
                        circle.setAttribute("cx", stroke.split(' ')[1].replace('L', ''));
                        circle.setAttribute("cy", stroke.split(' ')[2]);
                        circle.setAttribute("r", "10");
                        circle.setAttribute("fill", "#3b82f6");

                        const text = document.createElementNS("http://www.w3.org/2000/svg", "text");
                        text.setAttribute("x", stroke.split(' ')[1].replace('L', ''));
                        text.setAttribute("y", stroke.split(' ')[2]);
                        text.setAttribute("text-anchor", "middle");
                        text.setAttribute("dominant-baseline", "middle");
                        text.setAttribute("fill", "white");
                        text.setAttribute("font-size", "10");
                        text.textContent = (index + 1).toString();

                        strokeOrderSvg.appendChild(path);
                        strokeOrderSvg.appendChild(circle);
                        strokeOrderSvg.appendChild(text);
                    });
                }

                characterModal.classList.remove('hidden');
            }

            closeModalBtn.addEventListener('click', () => {
                characterModal.classList.add('hidden');
            });

            // Save sentence functionality
            saveSentenceBtn.addEventListener('click', function () {
                const sentence = document.getElementById('sentence-chinese').textContent;
                const pinyin = document.getElementById('sentence-pinyin').textContent;
                const translation = document.getElementById('sentence-translation').textContent;

                if (!sentence) return;

                // Check if already saved
                if (savedSentencesData.some(item => item.sentence === sentence)) {
                    alert('This sentence is already saved!');
                    return;
                }

                savedSentencesData.push({
                    sentence,
                    pinyin,
                    translation,
                    date: new Date().toISOString()
                });

                localStorage.setItem('chineseSavedSentences', JSON.stringify(savedSentencesData));
                updateSavedSentencesUI();

                // Change button style temporarily
                this.innerHTML = '<i class="fas fa-check mr-2"></i> 已保存';
                this.classList.remove('bg-gray-200', 'text-gray-700');
                this.classList.add('bg-green-500', 'text-white');

                setTimeout(() => {
                    this.innerHTML = '<i class="fas fa-bookmark mr-2"></i> 保存';
                    this.classList.remove('bg-green-500', 'text-white');
                    this.classList.add('bg-gray-200', 'text-gray-700');
                }, 1500);
            });

            // Update saved sentences UI
            function updateSavedSentencesUI() {
                if (savedSentencesData.length > 0) {
                    savedSentences.classList.remove('hidden');
                    noSaved.classList.add('hidden');

                    savedSentencesList.innerHTML = '';

                    savedSentencesData.forEach((item, index) => {
                        const li = document.createElement('li');
                        li.className = 'saved-sentence p-3 bg-gray-50 rounded cursor-pointer';
                        li.innerHTML = `
                            <div class="flex justify-between">
                                <div class="font-medium">${item.sentence}</div>
                                <button class="text-gray-400 hover:text-red-500" data-index="${index}">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                            <div class="text-sm text-gray-600">${item.pinyin}</div>
                            <div class="text-sm text-gray-500 mt-1">${item.translation}</div>
                        `;

                        // Add click handler to load saved sentence
                        li.addEventListener('click', function (e) {
                            if (e.target.tagName === 'BUTTON' || e.target.tagName === 'I') return;
                            input.value = item.sentence;
                            analyzeSentence(item.sentence);

                            // Scroll to top
                            window.scrollTo({
                                top: 0,
                                behavior: 'smooth'
                            });
                        });

                        // Add delete handler for the X button
                        const deleteBtn = li.querySelector('button');
                        deleteBtn.addEventListener('click', function (e) {
                            e.stopPropagation();
                            const index = parseInt(this.getAttribute('data-index'));
                            savedSentencesData.splice(index, 1);
                            localStorage.setItem('chineseSavedSentences', JSON.stringify(savedSentencesData));
                            updateSavedSentencesUI();
                        });

                        savedSentencesList.appendChild(li);
                    });
                } else {
                    savedSentences.classList.add('hidden');
                    noSaved.classList.remove('hidden');
                }
            }

            // Clear all saved sentences
            clearSavedBtn.addEventListener('click', function () {
                if (confirm('Are you sure you want to delete all saved sentences?')) {
                    savedSentencesData = [];
                    localStorage.setItem('chineseSavedSentences', JSON.stringify(savedSentencesData));
                    updateSavedSentencesUI();
                }
            });

            // Play audio functionality (simulated)
            playSentenceBtn.addEventListener('click', function () {
                const sentence = document.getElementById('sentence-chinese').textContent;

                // In a real app, this would use TTS or pre-recorded audio
                alert(`Playing pronunciation for: ${sentence}`);
            });

            playCharacterBtn.addEventListener('click', function () {
                const character = modalCharacter.textContent;

                // In a real app, this would use TTS or pre-recorded audio
                alert(`Playing pronunciation for: ${character}`);
            });

            // Expose functions and variables for testing
            app = {
                analyzeSentence,
                displayResults,
                splitSentence,
                addToneColors,
                showCharacterModal,
                updateSavedSentencesUI,
                dictionary,
                savedSentencesData,
                elements: {
                    form,
                    input,
                    results,
                    loading,
                    emptyState,
                    savedSentences,
                    noSaved,
                    savedSentencesList,
                    clearSavedBtn,
                    saveSentenceBtn,
                    playSentenceBtn,
                    characterModal,
                    closeModalBtn,
                    playCharacterBtn,
                    modalCharacter,
                    modalPinyin,
                    modalMeaning,
                    modalRadical,
                    modalStrokes,
                    strokeOrderSvg
                }
            };
        });

        // Test Suite
        function runTests() {
            // Setup Mocha
            mocha.setup('bdd');
            const expect = chai.expect;

            // Show test container
            document.getElementById('test-container').style.display = 'block';

            describe('Chinese Learning Companion Test Suite', function () {

                beforeEach(function () {
                    // Reset localStorage before each test
                    localStorage.removeItem('chineseSavedSentences');
                    app.savedSentencesData = [];

                    // Reset UI state
                    app.elements.input.value = '';
                    app.elements.results.classList.add('hidden');
                    app.elements.loading.classList.add('hidden');
                    app.elements.emptyState.classList.remove('hidden');
                    app.elements.characterModal.classList.add('hidden');

                    // Clear results
                    document.getElementById('sentence-chinese').textContent = '';
                    document.getElementById('sentence-pinyin').innerHTML = '';
                    document.getElementById('sentence-translation').textContent = '';
                    document.getElementById('character-cards').innerHTML = '';
                });

                describe('Form Input and Validation', function () {

                    it('should prevent form submission with empty input', function () {
                        app.elements.input.value = '';

                        // Simulate form submission
                        const event = new Event('submit');
                        let alertCalled = false;
                        const originalAlert = window.alert;
                        window.alert = function (msg) {
                            alertCalled = true;
                            expect(msg).to.equal('Please enter a Chinese sentence');
                        };

                        app.elements.form.dispatchEvent(event);

                        expect(alertCalled).to.be.true;
                        window.alert = originalAlert;
                    });

                    it('should accept valid Chinese input', function () {
                        app.elements.input.value = '你好';
                        expect(app.elements.input.value).to.equal('你好');
                    });

                    it('should trim whitespace from input', function () {
                        app.elements.input.value = '  你好  ';
                        const trimmed = app.elements.input.value.trim();
                        expect(trimmed).to.equal('你好');
                    });

                    it('should handle special characters and punctuation', function () {
                        app.elements.input.value = '你好，我学习中文！';
                        expect(app.elements.input.value).to.include('，');
                        expect(app.elements.input.value).to.include('！');
                    });
                });

                describe('Dictionary Data Structure', function () {

                    it('should have valid dictionary entries', function () {
                        expect(app.dictionary).to.be.an('object');
                        expect(Object.keys(app.dictionary).length).to.be.greaterThan(0);
                    });

                    it('should have required properties for single characters', function () {
                        const char = app.dictionary['你'];
                        expect(char).to.have.property('pinyin');
                        expect(char).to.have.property('tone');
                        expect(char).to.have.property('meaning');
                        expect(char).to.have.property('radical');
                        expect(char).to.have.property('strokes');
                    });

                    it('should have required properties for words', function () {
                        const word = app.dictionary['你好'];
                        expect(word).to.have.property('pinyin');
                        expect(word).to.have.property('tone');
                        expect(word).to.have.property('meaning');
                        expect(word).to.have.property('radical');
                        expect(word).to.have.property('strokes');
                        expect(word).to.have.property('isWord');
                        expect(word.isWord).to.be.true;
                    });

                    it('should have valid tone values', function () {
                        Object.values(app.dictionary).forEach(entry => {
                            if (Array.isArray(entry.tone)) {
                                entry.tone.forEach(tone => {
                                    expect(tone).to.be.within(1, 5);
                                });
                            } else {
                                expect(entry.tone).to.be.within(1, 5);
                            }
                        });
                    });
                });

                describe('Word Segmentation', function () {

                    it('should split single characters correctly', function () {
                        const result = app.splitSentence('你好');
                        expect(result).to.deep.equal(['你好']); // Should recognize as word
                    });

                    it('should prioritize multi-character words', function () {
                        const result = app.splitSentence('你好我');
                        expect(result).to.include('你好');
                        expect(result).to.include('我');
                    });

                    it('should handle unknown characters', function () {
                        const result = app.splitSentence('你好X');
                        expect(result).to.include('你好');
                        expect(result).to.include('X');
                    });

                    it('should handle empty input', function () {
                        const result = app.splitSentence('');
                        expect(result).to.be.an('array');
                        expect(result.length).to.equal(0);
                    });

                    it('should handle mixed content', function () {
                        const result = app.splitSentence('你好123');
                        expect(result).to.be.an('array');
                        expect(result.length).to.be.greaterThan(0);
                    });
                });

                describe('Tone Color Application', function () {

                    it('should apply correct tone colors for single tone', function () {
                        const result = app.addToneColors('nǐ', 3);
                        expect(result).to.include('tone-3');
                        expect(result).to.include('nǐ');
                    });

                    it('should apply correct tone colors for multiple tones', function () {
                        const result = app.addToneColors('nǐ hǎo', [3, 3]);
                        expect(result).to.include('tone-3');
                        expect(result).to.include('nǐ');
                        expect(result).to.include('hǎo');
                    });

                    it('should handle mismatched syllables and tones', function () {
                        const result = app.addToneColors('nǐ hǎo wǒ', [3, 3]);
                        expect(result).to.be.a('string');
                        // Should only process first two syllables
                        expect(result.split(' ').length).to.equal(2);
                    });

                    it('should handle empty pinyin', function () {
                        const result = app.addToneColors('', []);
                        expect(result).to.equal('');
                    });
                });

                describe('Sentence Analysis and Display', function () {

                    it('should display sentence correctly', function (done) {
                        const sentence = '你好';
                        app.displayResults(sentence);

                        setTimeout(() => {
                            expect(document.getElementById('sentence-chinese').textContent).to.equal(sentence);
                            expect(app.elements.results.classList.contains('hidden')).to.be.false;
                            done();
                        }, 100);
                    });

                    it('should generate pinyin for known words', function () {
                        app.displayResults('你好');
                        const pinyinElement = document.getElementById('sentence-pinyin');
                        expect(pinyinElement.innerHTML).to.include('tone-3');
                    });

                    it('should generate translation for known words', function () {
                        app.displayResults('你好');
                        const translationElement = document.getElementById('sentence-translation');
                        expect(translationElement.textContent).to.include('hello');
                    });

                    it('should create character cards', function () {
                        app.displayResults('你好');
                        const characterCards = document.getElementById('character-cards');
                        expect(characterCards.children.length).to.be.greaterThan(0);
                    });

                    it('should handle unknown characters gracefully', function () {
                        app.displayResults('你好X');
                        const characterCards = document.getElementById('character-cards');
                        expect(characterCards.children.length).to.be.greaterThan(0);

                        // Check if unknown character card exists
                        const cards = Array.from(characterCards.children);
                        const unknownCard = cards.find(card => card.textContent.includes('Unknown'));
                        expect(unknownCard).to.exist;
                    });
                });

                describe('Character Modal Functionality', function () {

                    it('should show modal for known characters', function () {
                        app.showCharacterModal('你');
                        expect(app.elements.characterModal.classList.contains('hidden')).to.be.false;
                        expect(app.elements.modalCharacter.textContent).to.equal('你');
                    });

                    it('should not show modal for unknown characters', function () {
                        app.showCharacterModal('X');
                        expect(app.elements.characterModal.classList.contains('hidden')).to.be.true;
                    });

                    it('should populate modal with character data', function () {
                        app.showCharacterModal('你');
                        expect(app.elements.modalPinyin.innerHTML).to.include('tone-3');
                        expect(app.elements.modalMeaning.textContent).to.include('you');
                        expect(app.elements.modalRadical.textContent).to.include('亻');
                        expect(app.elements.modalStrokes.textContent).to.include('7');
                    });

                    it('should handle word data in modal', function () {
                        app.showCharacterModal('你好');
                        expect(app.elements.modalCharacter.textContent).to.equal('你好');
                        expect(app.elements.modalMeaning.textContent).to.include('hello');
                    });

                    it('should close modal when close button is clicked', function () {
                        app.showCharacterModal('你');
                        app.elements.closeModalBtn.click();
                        expect(app.elements.characterModal.classList.contains('hidden')).to.be.true;
                    });
                });

                describe('Saved Sentences Management', function () {

                    beforeEach(function () {
                        // Clear saved sentences
                        app.savedSentencesData = [];
                        localStorage.removeItem('chineseSavedSentences');
                    });

                    it('should save a sentence to localStorage', function () {
                        // Setup sentence data
                        document.getElementById('sentence-chinese').textContent = '你好';
                        document.getElementById('sentence-pinyin').textContent = 'nǐ hǎo';
                        document.getElementById('sentence-translation').textContent = 'hello';

                        app.elements.saveSentenceBtn.click();

                        const saved = JSON.parse(localStorage.getItem('chineseSavedSentences'));
                        expect(saved).to.be.an('array');
                        expect(saved.length).to.equal(1);
                        expect(saved[0].sentence).to.equal('你好');
                    });

                    it('should prevent duplicate sentences', function () {
                        // Setup sentence data
                        document.getElementById('sentence-chinese').textContent = '你好';
                        document.getElementById('sentence-pinyin').textContent = 'nǐ hǎo';
                        document.getElementById('sentence-translation').textContent = 'hello';

                        // Add to saved data
                        app.savedSentencesData.push({
                            sentence: '你好',
                            pinyin: 'nǐ hǎo',
                            translation: 'hello',
                            date: new Date().toISOString()
                        });

                        let alertCalled = false;
                        const originalAlert = window.alert;
                        window.alert = function (msg) {
                            alertCalled = true;
                            expect(msg).to.equal('This sentence is already saved!');
                        };

                        app.elements.saveSentenceBtn.click();
                        expect(alertCalled).to.be.true;
                        window.alert = originalAlert;
                    });

                    it('should update UI after saving', function () {
                        // Setup sentence data
                        document.getElementById('sentence-chinese').textContent = '你好';
                        document.getElementById('sentence-pinyin').textContent = 'nǐ hǎo';
                        document.getElementById('sentence-translation').textContent = 'hello';

                        app.elements.saveSentenceBtn.click();
                        app.updateSavedSentencesUI();

                        expect(app.elements.savedSentences.classList.contains('hidden')).to.be.false;
                        expect(app.elements.noSaved.classList.contains('hidden')).to.be.true;
                    });

                    it('should clear all saved sentences', function () {
                        // Add some test data
                        app.savedSentencesData = [
                            { sentence: '你好', pinyin: 'nǐ hǎo', translation: 'hello', date: new Date().toISOString() }
                        ];
                        localStorage.setItem('chineseSavedSentences', JSON.stringify(app.savedSentencesData));

                        let confirmCalled = false;
                        const originalConfirm = window.confirm;
                        window.confirm = function () {
                            confirmCalled = true;
                            return true;
                        };

                        app.elements.clearSavedBtn.click();

                        expect(confirmCalled).to.be.true;
                        expect(app.savedSentencesData.length).to.equal(0);
                        window.confirm = originalConfirm;
                    });
                });

                describe('UI State Management', function () {

                    it('should show loading state during analysis', function () {
                        app.analyzeSentence('你好');
                        expect(app.elements.loading.classList.contains('hidden')).to.be.false;
                        expect(app.elements.emptyState.classList.contains('hidden')).to.be.true;
                        expect(app.elements.results.classList.contains('hidden')).to.be.true;
                    });

                    it('should show results after analysis completes', function (done) {
                        app.analyzeSentence('你好');

                        setTimeout(() => {
                            expect(app.elements.loading.classList.contains('hidden')).to.be.true;
                            expect(app.elements.results.classList.contains('hidden')).to.be.false;
                            done();
                        }, 1000);
                    });

                    it('should show empty state initially', function () {
                        expect(app.elements.emptyState.classList.contains('hidden')).to.be.false;
                        expect(app.elements.results.classList.contains('hidden')).to.be.true;
                    });
                });

                describe('Audio Functionality', function () {

                    it('should trigger sentence audio playback', function () {
                        document.getElementById('sentence-chinese').textContent = '你好';

                        let alertCalled = false;
                        const originalAlert = window.alert;
                        window.alert = function (msg) {
                            alertCalled = true;
                            expect(msg).to.include('Playing pronunciation for: 你好');
                        };

                        app.elements.playSentenceBtn.click();
                        expect(alertCalled).to.be.true;
                        window.alert = originalAlert;
                    });

                    it('should trigger character audio playback', function () {
                        app.showCharacterModal('你');

                        let alertCalled = false;
                        const originalAlert = window.alert;
                        window.alert = function (msg) {
                            alertCalled = true;
                            expect(msg).to.include('Playing pronunciation for: 你');
                        };

                        app.elements.playCharacterBtn.click();
                        expect(alertCalled).to.be.true;
                        window.alert = originalAlert;
                    });
                });

                describe('Edge Cases and Error Handling', function () {

                    it('should handle very long sentences', function () {
                        const longSentence = '你好'.repeat(50);
                        app.displayResults(longSentence);
                        expect(document.getElementById('sentence-chinese').textContent).to.equal(longSentence);
                    });

                    it('should handle sentences with only punctuation', function () {
                        const punctuation = '，。！？';
                        const result = app.splitSentence(punctuation);
                        expect(result).to.be.an('array');
                        expect(result.length).to.equal(4);
                    });

                    it('should handle mixed language input', function () {
                        const mixed = '你好Hello世界World';
                        const result = app.splitSentence(mixed);
                        expect(result).to.be.an('array');
                        expect(result.length).to.be.greaterThan(0);
                    });

                    it('should handle numbers and special characters', function () {
                        const special = '你好123@#$';
                        const result = app.splitSentence(special);
                        expect(result).to.be.an('array');
                    });

                    it('should handle null and undefined inputs gracefully', function () {
                        expect(() => app.splitSentence(null)).to.not.throw();
                        expect(() => app.splitSentence(undefined)).to.not.throw();
                    });

                    it('should handle malformed dictionary entries', function () {
                        // Test with missing properties
                        const originalDict = app.dictionary;
                        app.dictionary = {
                            'test': { pinyin: 'test' } // Missing other required properties
                        };

                        expect(() => app.displayResults('test')).to.not.throw();

                        // Restore original dictionary
                        app.dictionary = originalDict;
                    });

                    it('should handle localStorage errors', function () {
                        // Mock localStorage to throw error
                        const originalSetItem = localStorage.setItem;
                        localStorage.setItem = function () {
                            throw new Error('Storage quota exceeded');
                        };

                        document.getElementById('sentence-chinese').textContent = '你好';
                        document.getElementById('sentence-pinyin').textContent = 'nǐ hǎo';
                        document.getElementById('sentence-translation').textContent = 'hello';

                        expect(() => app.elements.saveSentenceBtn.click()).to.not.throw();

                        // Restore original localStorage
                        localStorage.setItem = originalSetItem;
                    });
                });

                describe('Integration Tests', function () {

                    it('should complete full workflow: input -> analyze -> save -> load', function (done) {
                        // Step 1: Input sentence
                        app.elements.input.value = '你好';

                        // Step 2: Analyze sentence
                        app.analyzeSentence('你好');

                        setTimeout(() => {
                            // Step 3: Verify analysis results
                            expect(document.getElementById('sentence-chinese').textContent).to.equal('你好');
                            expect(app.elements.results.classList.contains('hidden')).to.be.false;

                            // Step 4: Save sentence
                            app.elements.saveSentenceBtn.click();

                            // Step 5: Verify saved
                            const saved = JSON.parse(localStorage.getItem('chineseSavedSentences'));
                            expect(saved.length).to.equal(1);

                            // Step 6: Update UI and verify saved sentences display
                            app.updateSavedSentencesUI();
                            expect(app.elements.savedSentences.classList.contains('hidden')).to.be.false;

                            done();
                        }, 1000);
                    });

                    it('should handle character card click -> modal -> close workflow', function () {
                        // Step 1: Display results with character cards
                        app.displayResults('你');

                        // Step 2: Click character card (simulate)
                        app.showCharacterModal('你');

                        // Step 3: Verify modal is open
                        expect(app.elements.characterModal.classList.contains('hidden')).to.be.false;
                        expect(app.elements.modalCharacter.textContent).to.equal('你');

                        // Step 4: Close modal
                        app.elements.closeModalBtn.click();

                        // Step 5: Verify modal is closed
                        expect(app.elements.characterModal.classList.contains('hidden')).to.be.true;
                    });

                    it('should handle multiple sentence analysis in sequence', function (done) {
                        const sentences = ['你好', '我', '学习'];
                        let currentIndex = 0;

                        function analyzeNext() {
                            if (currentIndex >= sentences.length) {
                                done();
                                return;
                            }

                            const sentence = sentences[currentIndex];
                            app.analyzeSentence(sentence);

                            setTimeout(() => {
                                expect(document.getElementById('sentence-chinese').textContent).to.equal(sentence);
                                currentIndex++;
                                analyzeNext();
                            }, 900);
                        }

                        analyzeNext();
                    });

                    it('should maintain state consistency across operations', function () {
                        // Initial state
                        expect(app.savedSentencesData).to.be.an('array');
                        expect(app.savedSentencesData.length).to.equal(0);

                        // Add sentence
                        document.getElementById('sentence-chinese').textContent = '你好';
                        document.getElementById('sentence-pinyin').textContent = 'nǐ hǎo';
                        document.getElementById('sentence-translation').textContent = 'hello';
                        app.elements.saveSentenceBtn.click();

                        // Verify state
                        expect(app.savedSentencesData.length).to.equal(1);

                        // Clear and verify
                        const originalConfirm = window.confirm;
                        window.confirm = () => true;
                        app.elements.clearSavedBtn.click();
                        expect(app.savedSentencesData.length).to.equal(0);
                        window.confirm = originalConfirm;
                    });
                });

                describe('Performance and Stress Tests', function () {

                    it('should handle rapid successive analyses', function (done) {
                        let completedCount = 0;
                        const totalTests = 5;

                        for (let i = 0; i < totalTests; i++) {
                            setTimeout(() => {
                                app.analyzeSentence('你好');
                                completedCount++;
                                if (completedCount === totalTests) {
                                    expect(document.getElementById('sentence-chinese').textContent).to.equal('你好');
                                    done();
                                }
                            }, i * 100);
                        }
                    });

                    it('should handle large number of saved sentences', function () {
                        // Add many sentences
                        for (let i = 0; i < 100; i++) {
                            app.savedSentencesData.push({
                                sentence: `测试${i}`,
                                pinyin: `cè shì ${i}`,
                                translation: `test ${i}`,
                                date: new Date().toISOString()
                            });
                        }

                        expect(() => app.updateSavedSentencesUI()).to.not.throw();
                        expect(app.savedSentencesData.length).to.equal(100);
                    });
                });
            });

            // Run the tests
            mocha.run();
        }

        // Add test runner button
        function addTestButton() {
            const testButton = document.createElement('button');
            testButton.textContent = 'Run Tests';
            testButton.className = 'fixed top-4 right-4 bg-red-500 text-white px-4 py-2 rounded-lg hover:bg-red-600 z-50';
            testButton.onclick = runTests;
            document.body.appendChild(testButton);
        }

        // Add test button when page loads
        setTimeout(addTestButton, 1000);
    </script>
</body>

</html>