<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bug Demonstration - Chinese Learning Companion</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .bug-test { background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; padding: 15px; margin: 10px 0; }
        .bug-title { color: #dc3545; font-weight: bold; margin-bottom: 10px; }
        .test-button { background: #007bff; color: white; padding: 8px 16px; border: none; border-radius: 3px; cursor: pointer; margin: 5px; }
        .test-button:hover { background: #0056b3; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 3px; margin: 5px 0; }
        .success { color: #155724; background: #d4edda; padding: 10px; border-radius: 3px; margin: 5px 0; }
        .result { margin: 10px 0; padding: 10px; border-radius: 3px; background: #e9ecef; }
    </style>
</head>
<body>
    <h1>Bug Demonstration for Chinese Learning Companion</h1>
    <p>This page demonstrates the bugs found in the current implementation.</p>

    <div class="bug-test">
        <div class="bug-title">Bug 1: Null/Undefined Input Handling</div>
        <p>Testing splitSentence function with null and undefined inputs</p>
        <button class="test-button" onclick="testNullInput()">Test Null Input</button>
        <div id="null-test-result"></div>
    </div>

    <div class="bug-test">
        <div class="bug-title">Bug 2: Empty Pinyin Handling</div>
        <p>Testing addToneColors function with empty strings</p>
        <button class="test-button" onclick="testEmptyPinyin()">Test Empty Pinyin</button>
        <div id="empty-pinyin-result"></div>
    </div>

    <div class="bug-test">
        <div class="bug-title">Bug 3: Stroke Order Parsing</div>
        <p>Testing stroke order SVG generation with malformed data</p>
        <button class="test-button" onclick="testStrokeOrder()">Test Stroke Order Bug</button>
        <div id="stroke-order-result"></div>
    </div>

    <div class="bug-test">
        <div class="bug-title">Bug 4: Array Index Bug in Delete</div>
        <p>Testing saved sentences deletion with multiple items</p>
        <button class="test-button" onclick="testDeleteBug()">Test Delete Bug</button>
        <div id="delete-bug-result"></div>
    </div>

    <div class="bug-test">
        <div class="bug-title">Bug 5: Data Type Inconsistency</div>
        <p>Testing modal display with inconsistent data types</p>
        <button class="test-button" onclick="testDataTypes()">Test Data Type Issues</button>
        <div id="data-type-result"></div>
    </div>

    <!-- Load the main app in a hidden iframe -->
    <iframe id="app-frame" src="sentenceDecomposer.html" style="display: none;"></iframe>

    <script>
        let app = null;
        let appWindow = null;

        // Wait for app to load
        document.getElementById('app-frame').onload = function() {
            setTimeout(() => {
                appWindow = document.getElementById('app-frame').contentWindow;
                app = appWindow.app;
                if (app) {
                    document.body.insertAdjacentHTML('afterbegin', 
                        '<div class="success">✓ App loaded successfully. Ready to test bugs.</div>');
                } else {
                    document.body.insertAdjacentHTML('afterbegin', 
                        '<div class="error">✗ Failed to load app. Cannot run tests.</div>');
                }
            }, 2000);
        };

        function testNullInput() {
            const resultDiv = document.getElementById('null-test-result');
            try {
                // Test with null
                const result1 = app.splitSentence(null);
                resultDiv.innerHTML = '<div class="error">BUG CONFIRMED: Function should have crashed but didn\'t. Result: ' + JSON.stringify(result1) + '</div>';
            } catch (error) {
                resultDiv.innerHTML = '<div class="error">BUG CONFIRMED: TypeError when passing null: ' + error.message + '</div>';
            }

            try {
                // Test with undefined
                const result2 = app.splitSentence(undefined);
                resultDiv.innerHTML += '<div class="error">BUG CONFIRMED: Function should have crashed but didn\'t. Result: ' + JSON.stringify(result2) + '</div>';
            } catch (error) {
                resultDiv.innerHTML += '<div class="error">BUG CONFIRMED: TypeError when passing undefined: ' + error.message + '</div>';
            }
        }

        function testEmptyPinyin() {
            const resultDiv = document.getElementById('empty-pinyin-result');
            try {
                const result = app.addToneColors('', []);
                resultDiv.innerHTML = '<div class="result">Result with empty string: "' + result + '"</div>';
                
                // Test with space-only string
                const result2 = app.addToneColors('   ', [1, 2]);
                resultDiv.innerHTML += '<div class="result">Result with spaces: "' + result2 + '"</div>';
                
                // Test mismatched syllables and tones
                const result3 = app.addToneColors('nǐ hǎo wǒ', [3]);
                resultDiv.innerHTML += '<div class="error">BUG: Mismatched syllables/tones - should handle gracefully: "' + result3 + '"</div>';
            } catch (error) {
                resultDiv.innerHTML = '<div class="error">Error in addToneColors: ' + error.message + '</div>';
            }
        }

        function testStrokeOrder() {
            const resultDiv = document.getElementById('stroke-order-result');
            try {
                // Create malformed stroke data
                const badStrokeData = {
                    pinyin: "test",
                    tone: 1,
                    meaning: "test",
                    radical: "test",
                    strokes: 1,
                    strokeOrder: [
                        "invalid path data",
                        "M", // incomplete path
                        "L20" // missing coordinates
                    ]
                };

                // Temporarily modify dictionary
                const originalDict = app.dictionary;
                app.dictionary = { 'test': badStrokeData };

                app.showCharacterModal('test');
                resultDiv.innerHTML = '<div class="error">BUG CONFIRMED: Stroke order parsing should have failed but didn\'t crash</div>';
                
                // Restore dictionary
                app.dictionary = originalDict;
            } catch (error) {
                resultDiv.innerHTML = '<div class="error">BUG CONFIRMED: Stroke order parsing error: ' + error.message + '</div>';
            }
        }

        function testDeleteBug() {
            const resultDiv = document.getElementById('delete-bug-result');
            
            // Setup test data
            app.savedSentencesData = [
                { sentence: '你好', pinyin: 'nǐ hǎo', translation: 'hello', date: new Date().toISOString() },
                { sentence: '再见', pinyin: 'zài jiàn', translation: 'goodbye', date: new Date().toISOString() },
                { sentence: '谢谢', pinyin: 'xiè xiè', translation: 'thank you', date: new Date().toISOString() }
            ];

            // Update UI to create the list
            app.updateSavedSentencesUI();

            resultDiv.innerHTML = '<div class="result">Created 3 saved sentences. Try deleting the first one, then the second. The indices will be wrong.</div>';
            resultDiv.innerHTML += '<div class="error">BUG: After deleting first item, clicking delete on second item will delete wrong sentence due to stale indices.</div>';
        }

        function testDataTypes() {
            const resultDiv = document.getElementById('data-type-result');
            
            // Test with mixed data types
            const mixedData = {
                pinyin: ["nǐ", "hǎo"], // array
                tone: [3, 3], // array
                meaning: "hello", // string
                radical: ["亻", "女"], // array
                strokes: 7 // number (should be array for consistency)
            };

            const originalDict = app.dictionary;
            app.dictionary = { 'mixed': mixedData };

            try {
                app.showCharacterModal('mixed');
                resultDiv.innerHTML = '<div class="error">BUG CONFIRMED: Inconsistent data types handled, but may display incorrectly</div>';
            } catch (error) {
                resultDiv.innerHTML = '<div class="error">BUG CONFIRMED: Data type inconsistency caused error: ' + error.message + '</div>';
            }

            app.dictionary = originalDict;
        }

        // Auto-run a quick test when page loads
        setTimeout(() => {
            if (app) {
                document.body.insertAdjacentHTML('beforeend', 
                    '<div style="margin-top: 20px; padding: 15px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px;">' +
                    '<strong>Quick Test Results:</strong><br>' +
                    'Dictionary entries: ' + Object.keys(app.dictionary).length + '<br>' +
                    'Functions exposed: ' + Object.keys(app).length + '<br>' +
                    'Ready for manual testing!' +
                    '</div>');
            }
        }, 3000);
    </script>
</body>
</html>
