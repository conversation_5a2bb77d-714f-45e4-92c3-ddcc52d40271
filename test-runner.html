<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Runner for Chinese Learning Companion</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mocha/10.2.0/mocha.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/chai/4.3.7/chai.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/mocha/10.2.0/mocha.min.css">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-info { background: #f0f8ff; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .run-button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; }
        .run-button:hover { background: #0056b3; }
        iframe { width: 100%; height: 400px; border: 1px solid #ccc; margin: 20px 0; }
    </style>
</head>
<body>
    <h1>Chinese Learning Companion - Test Suite</h1>
    
    <div class="test-info">
        <h2>Test Information</h2>
        <p>This test suite will automatically run comprehensive tests on the Chinese Learning Companion application.</p>
        <p><strong>Test Categories:</strong></p>
        <ul>
            <li>Form Input and Validation</li>
            <li>Dictionary Data Structure</li>
            <li>Word Segmentation</li>
            <li>Tone Color Application</li>
            <li>Sentence Analysis and Display</li>
            <li>Character Modal Functionality</li>
            <li>Saved Sentences Management</li>
            <li>UI State Management</li>
            <li>Audio Functionality</li>
            <li>Edge Cases and Error Handling</li>
            <li>Integration Tests</li>
            <li>Performance and Stress Tests</li>
        </ul>
        <button class="run-button" onclick="runTests()">Run All Tests</button>
    </div>

    <!-- Load the main application in an iframe for testing -->
    <iframe id="app-frame" src="sentenceDecomposer.html"></iframe>

    <!-- Test Results -->
    <div id="mocha"></div>

    <script>
        let appWindow = null;
        let app = null;

        function runTests() {
            // Setup Mocha
            mocha.setup('bdd');
            const expect = chai.expect;

            // Get reference to the app in the iframe
            const iframe = document.getElementById('app-frame');
            appWindow = iframe.contentWindow;
            
            // Wait for the app to load
            iframe.onload = function() {
                setTimeout(() => {
                    app = appWindow.app;
                    
                    if (!app) {
                        console.error('App not loaded properly');
                        return;
                    }

                    // Run a quick smoke test first
                    describe('Smoke Tests', function() {
                        it('should load the application', function() {
                            expect(app).to.exist;
                            expect(app.dictionary).to.exist;
                            expect(app.elements).to.exist;
                        });

                        it('should have all required DOM elements', function() {
                            expect(app.elements.form).to.exist;
                            expect(app.elements.input).to.exist;
                            expect(app.elements.results).to.exist;
                            expect(app.elements.loading).to.exist;
                        });

                        it('should have dictionary data', function() {
                            expect(Object.keys(app.dictionary).length).to.be.greaterThan(0);
                        });
                    });

                    // Test basic functionality
                    describe('Basic Functionality Tests', function() {
                        beforeEach(function() {
                            // Reset state
                            app.elements.input.value = '';
                            app.elements.results.classList.add('hidden');
                            app.elements.loading.classList.add('hidden');
                            app.elements.emptyState.classList.remove('hidden');
                        });

                        it('should split sentences correctly', function() {
                            const result = app.splitSentence('你好');
                            expect(result).to.be.an('array');
                            expect(result).to.include('你好');
                        });

                        it('should apply tone colors', function() {
                            const result = app.addToneColors('nǐ', 3);
                            expect(result).to.include('tone-3');
                        });

                        it('should display results', function() {
                            app.displayResults('你好');
                            expect(appWindow.document.getElementById('sentence-chinese').textContent).to.equal('你好');
                        });

                        it('should show character modal', function() {
                            app.showCharacterModal('你');
                            expect(app.elements.characterModal.classList.contains('hidden')).to.be.false;
                        });
                    });

                    // Test error conditions
                    describe('Error Handling Tests', function() {
                        it('should handle empty input', function() {
                            const result = app.splitSentence('');
                            expect(result).to.be.an('array');
                            expect(result.length).to.equal(0);
                        });

                        it('should handle unknown characters', function() {
                            const result = app.splitSentence('XYZ');
                            expect(result).to.be.an('array');
                            expect(result.length).to.equal(3);
                        });

                        it('should handle null input gracefully', function() {
                            expect(() => app.splitSentence(null)).to.not.throw();
                        });
                    });

                    // Run the tests
                    mocha.run();
                }, 2000); // Wait 2 seconds for app to fully initialize
            };
        }

        // Auto-run tests when page loads
        window.onload = function() {
            setTimeout(runTests, 1000);
        };
    </script>
</body>
</html>
